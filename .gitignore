#dependencies
/node_modules
# pnpm-lock.yaml
yarn.lock
package-lock.json
/.pnp
.pnp.js

# environment variables
.env
.env.local
.env.development.local
.env.test.local
.env.production.local
.env.*

# misc
.DS_Store

# debug
npm-debug.log*
yarn-debug.log*
yarn-error.log*
.pnpm-debug.log*

# typescript
*.tsbuildinfo

# idea files
.idea

# Editor directories and files
.vscode/*
!.vscode/extensions.json
.idea
.DS_Store
*.suo
*.ntvs*
*.njsproj
*.sln
*.sw?

# lighthouse
lighthouse-*