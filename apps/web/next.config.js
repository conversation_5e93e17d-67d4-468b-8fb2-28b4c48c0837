/**
 * Run `build` or `dev` with `SKIP_ENV_VALIDATION` to skip env validation. This is especially useful
 * for Docker builds.
 */
import createMDX from "@next/mdx";
import path from "path";
import "./src/env.js";

/** @type {import("next").NextConfig} */
const config = {
  // For Docker
  output: "standalone",
  pageExtensions: ["jsx", "js", "ts", "tsx", "mdx"],
  // Performance optimizations
  experimental: {
    optimizePackageImports: ["react", "react-dom", "lucide-react"],
    webVitalsAttribution: ["CLS", "LCP"],
  },
  // Remote image optimization
  images: {
    remotePatterns: [
      {
        protocol: "https",
        hostname: "res.cloudinary.com",
        port: "",
        pathname: "/**",
      },
    ],
    formats: ["image/webp", "image/avif"],
    minimumCacheTTL: 31536000, // 1 year
  },
  // Logging
  logging: {
    fetches: {
      fullUrl: true,
    },
  },
  // Remove console.log in production
  compiler: {
    removeConsole: {
      exclude: ["error", "warn"],
    },
  },
  // Bundle analyzer for production builds
  ...(process.env.ANALYZE === "true" && {
    webpack: (config) => {
      config.resolve.alias = {
        ...config.resolve.alias,
        "@": path.resolve(__dirname, "src"),
      };
      return config;
    },
  }),
  // Headers for better caching and security
  async headers() {
    return [
      {
        source: "/(.*)",
        headers: [
          {
            key: "X-Content-Type-Options",
            value: "nosniff",
          },
          {
            key: "X-Frame-Options",
            value: "DENY",
          },
          {
            key: "X-XSS-Protection",
            value: "1; mode=block",
          },
        ],
      },
      {
        source:
          "/(.*)\\.(js|css|woff|woff2|eot|ttf|otf|png|jpg|jpeg|gif|ico|svg|webp|avif)",
        headers: [
          {
            key: "Cache-Control",
            value: "public, max-age=31536000, immutable",
          },
        ],
      },
    ];
  },
};

// mdx support
const withMDX = createMDX({
  extension: /\.mdx?$/,
});
export default withMDX(config);
