"use client";
import { useReportWebVitals } from "next/web-vitals";

type Metric = {
  id: string;
  name: string;
  value: number;
};

export function WebVitals(): null {
  useReportWebVitals((metric: Metric) => {
    // Only log in development or when explicitly enabled
    if (
      process.env.NODE_ENV === "development" ||
      process.env.NEXT_PUBLIC_DEBUG_VITALS === "true"
    ) {
      console.log("Web Vitals:", metric);
    }

    // Send to analytics service in production
    if (process.env.NODE_ENV === "production") {
      // You can send to Google Analytics, Vercel Analytics, or other services
      // Example for Google Analytics 4:
      if (typeof window !== "undefined" && window.gtag !== undefined) {
        window.gtag("event", metric.name, {
          custom_map: { metric_id: "custom_metric" },
          value: Math.round(
            metric.name === "CLS" ? metric.value * 1000 : metric.value,
          ),
          event_category: "Web Vitals",
          event_label: metric.id,
          non_interaction: true,
        });
      }
    }
  });

  return null;
}

// Type declaration for gtag
declare global {
  // eslint-disable-next-line @typescript-eslint/consistent-type-definitions
  interface Window {
    gtag?: (
      command: string,
      targetId: string,
      config?: Record<string, unknown>,
    ) => void;
  }
}
