import Image from "next/image";
import Link from "next/link";

export function SocialMediaList({
  alt,
  icon,
  link,
}: {
  alt: string;
  icon: string;
  link: string;
}): React.ReactElement {
  return (
    <li className="cursor-pointer transition-all duration-200 ease-in hover:scale-[1.2] active:scale-90">
      <a href={link} target="#_blank" rel="noopener noreferrer">
        <Image src={icon} alt={alt} width={21} height={21} />
      </a>
    </li>
  );
}

export function LinkList({
  url,
  name,
}: {
  url: string;
  name: string;
}): React.ReactElement {
  return (
    <li className="cursor-pointer duration-200 ease-in-out hover:font-bold">
      <Link href={url}>{name}</Link>
    </li>
  );
}
