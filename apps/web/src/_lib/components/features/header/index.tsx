import { WhatsappURL } from "@web/_lib/constants";
import { <PERSON><PERSON> } from "@web/_lib/components/ui";
import { MenuList } from "./menu-list";
import { MobileMenu } from "./menu-mobile";
import { ThemeSwitcher } from "./theme-switcher";

export function Header(): React.ReactElement {
  return (
    <header
      id="header"
      className="bg-light-card text-light-text dark:bg-dark-card dark:text-dark-text sticky top-0 z-50 flex w-full justify-center opacity-95 shadow-2xs backdrop-blur-sm"
    >
      <span className="flex h-20 w-full max-w-7xl items-center justify-between p-5 md:p-4">
        <Button
          href="/"
          variant="link"
          className="text-light-text dark:text-dark-text text-xl font-black no-underline"
        >
          @Lee.
        </Button>
        <MenuList />
        <div id="header-cta" className="flex items-center gap-4">
          <ThemeSwitcher />
          <Button
            href={WhatsappURL}
            external={true}
            variant="outline"
            className="rounded-full"
          >
            Contact Me
          </Button>
          <MobileMenu />
        </div>
      </span>
    </header>
  );
}
