"use client";
import Image from "next/image";
import { useEffect, useState } from "react";
import { useTheme } from "next-themes";
import { SunIcon, EclipseIcon } from "lucide-react";
import { Button } from "@web/_lib/components/ui";

export function ThemeSwitcher(): React.ReactElement | undefined {
  const [mounted, setMounted] = useState(false);
  const { theme, setTheme } = useTheme();

  // side-effect to ensure the component is mounted before accessing the theme
  useEffect(() => {
    setMounted(true);
  }, []);

  if (!mounted) {
    return (
      <Button
        size="icon"
        variant="outline"
        className="rounded-full"
        aria-label="Loading theme switcher"
      >
        <Image
          alt="Loading theme switcher"
          src="data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIyNCIgaGVpZ2h0PSIyNCIgdmlld0JveD0iMCAwIDI0IDI0IiBmaWxsPSJub25lIiBzdHJva2U9ImN1cnJlbnRDb2xvciIgc3Ryb2tlLXdpZHRoPSIyIiBzdHJva2UtbGluZWNhcD0icm91bmQiIHN0cm9rZS1saW5lam9pbj0icm91bmQiIGNsYXNzPSJsdWNpZGUgbHVjaWRlLXN1bi1pY29uIGx1Y2lkZS1zdW4iPjxjaXJjbGUgY3g9IjEyIiBjeT0iMTIiIHI9IjQiLz48cGF0aCBkPSJNMTIgMnYyIi8+PHBhdGggZD0iTTEyIDIwdjIiLz48cGF0aCBkPSJtNC45MyA0LjkzIDEuNDEgMS40MSIvPjxwYXRoIGQ9Im0xNy42NiAxNy42NiAxLjQxIDEuNDEiLz48cGF0aCBkPSJNMiAxMmgyIi8+PHBhdGggZD0iTTIwIDEyaDIiLz48cGF0aCBkPSJtNi4zNCAxNy42Ni0xLjQxIDEuNDEiLz48cGF0aCBkPSJtMTkuMDcgNC45My0xLjQxIDEuNDEiLz48L3N2Zz4="
          priority={false}
          sizes="20x20"
          width={20}
          height={20}
        />
      </Button>
    ); // Prevents hydration mismatch & content layout shift
  }

  //   Render the theme switcher icon based on the current theme
  if (theme === "dark") {
    return (
      <Button
        size="icon"
        variant="outline"
        className="rounded-full"
        aria-label="Switch to light theme"
        onClick={() => {
          setTheme("light");
        }}
      >
        <EclipseIcon className="size-5" />
      </Button>
    );
  }

  // Default to light theme icon
  if (theme === "light" || theme === "system") {
    return (
      <Button
        size="icon"
        variant="outline"
        className="rounded-full"
        aria-label="Switch to dark theme"
        onClick={() => {
          setTheme("dark");
        }}
      >
        <SunIcon className="size-5" />
      </Button>
    );
  }
}
