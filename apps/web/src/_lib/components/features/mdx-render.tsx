import { getMDXContent } from "@web/_lib/utils";
import { TableOfContents } from "./table-of-contents";

export async function MDXRenderer({
  source,
}: {
  source: string;
}): Promise<React.ReactElement> {
  const { content, toc } = await getMDXContent(source);
  return (
    <main className="m-auto flex h-full w-full max-w-7xl flex-col gap-4 pb-20 md:flex-row md:items-start">
      <nav className="dark:bg-dark-card bg-light-card sticky top-20 rounded-sm opacity-95 shadow-2xs backdrop-blur-sm md:top-24 md:w-[25%]">
        <TableOfContents show={true} headings={toc} />
      </nav>
      <aside className="prose dark:prose-invert max-w-none flex-1 px-6 md:pt-4">
        {content}
      </aside>
    </main>
  );
}
