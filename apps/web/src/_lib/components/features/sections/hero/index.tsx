import { SparklesIcon, SquareArrowOutUpRightIcon } from "lucide-react";
import { WhatsappURL } from "@web/_lib/constants";
import { But<PERSON> } from "@web/_lib/components/ui";
import HeroCard from "./card";

export function HeroSection(): React.ReactElement {
  return (
    <section
      id="hero"
      className="text-light-text dark:text-dark-text flex flex-col gap-12 px-4 md:flex-row md:gap-4"
    >
      <div
        id="hero-content"
        className="flex flex-1 flex-col justify-between gap-6 md:gap-0"
      >
        <h1 className="text-4xl font-semibold text-pretty capitalize md:text-7xl">
          Let&apos;s Turn Your
          <span className="text-light-peach-700 relative cursor-pointer">
            &nbsp;Visions&nbsp;
            <SparklesIcon className="text-light-peach-700 absolute top-2 -right-2.5 size-3.5 md:size-5.5" />
          </span>
          &nbsp;&nbsp;into Smart Tech That Works for You
        </h1>
        <p className="text-base text-balance md:text-pretty">
          Hi, I&apos;m Ali — I build websites that rank, apps that perform, and
          platforms that scale. Whether you&apos;re starting with a big idea or
          a small pain point, I&apos;ll help turn your idea into digital
          solutions that work for you.
        </p>
        <div id="hero-cta" className="flex gap-4">
          <Button href={WhatsappURL} external={true}>
            Let&apos;s Talk!
          </Button>
          <Button href="/about" variant="outline">
            About Me
            <SquareArrowOutUpRightIcon className="size-4" />
          </Button>
        </div>
      </div>
      <HeroCard />
    </section>
  );
}
