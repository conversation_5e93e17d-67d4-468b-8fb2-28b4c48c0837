import {
  userJ<PERSON>ney,
  type UserJourneyType,
} from "@web/_lib/models/user-journey";
import JurneyCard from "./card";

export function JourneySection(): React.ReactElement {
  return (
    <section
      id="why-choose-me"
      className="text-light-text dark:text-dark-text flex flex-col gap-6 px-4"
    >
      <div id="why-choose-me-left-content" className="text-center">
        <h1 className="pb-3 text-3xl font-semibold capitalize">
          Why Team Up with Me?
        </h1>
        <p className="text-md px-4 text-pretty md:px-0">
          I&apos;m not just here to code — I&apos;m here to understand your
          goals, solve real problems, and build software that works for you.
        </p>
      </div>
      <div
        id="why-choose-me-right-content"
        className="grid grid-cols-1 gap-4 md:grid-cols-3"
      >
        {userJourney.map((item: UserJourneyType) => (
          <JurneyCard
            key={item.id}
            title={item.title}
            description={item.description}
            icon={item.icon}
          />
        ))}
      </div>
    </section>
  );
}
