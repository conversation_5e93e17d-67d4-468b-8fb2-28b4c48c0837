import { twMerge } from "tailwind-merge";
import { serviceIconsMap } from "./icons";

type CardProps = {
  no: string;
  title: string;
  description: string;
  background?: string;
  showDetails?: boolean;
  icon?: string;
  className?: string;
};

export function Card({
  no,
  title,
  description,
  background,
  icon,
  className,
}: CardProps): React.ReactElement {
  return (
    <div
      id={title}
      className={twMerge(
        "group text-light-text relative flex min-h-98 cursor-pointer flex-col justify-end rounded-xl p-6",
        className,
        background,
      )}
    >
      <div className="bg-dark-card dark:bg-dark-background border-dark-border absolute top-4 left-4 rounded-full border p-2.5">
        {/* {serviceIconsMap.get(icon)?.element} */}
      </div>
      <h1 className="text-lg font-bold">{no}</h1>
      <h3 className="text-xl font-bold">{title}</h3>
      <span
        id="divider"
        className="bg-dark-border my-2 h-[0.1rem] w-full rounded-full opacity-0 duration-300 ease-in-out group-hover:opacity-100"
      ></span>
      <p className="h-0 text-sm text-pretty opacity-0 duration-300 ease-in-out group-hover:h-3/6 group-hover:opacity-100">
        {description}
      </p>
    </div>
  );
}
