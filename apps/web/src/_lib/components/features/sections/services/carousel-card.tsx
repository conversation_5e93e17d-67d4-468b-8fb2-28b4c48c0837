import { twMerge } from "tailwind-merge";
import { useStyles } from "@web/_lib/components/ui/carousel/hooks";
import type { ServicesType } from "@web/_lib/models/services";

type CarouselCardsProps = {
  data: {
    index: number;
    currentIndex: number;
    length: number;
    depth: number;
    spacing: number;
  };
  service: ServicesType;
  style?: React.CSSProperties;
};

export function CarouselCards({
  service,
  style,
  data: { index, currentIndex, length, depth, spacing },
}: CarouselCardsProps): React.ReactElement {
  const [styles, zIndex] = useStyles(
    index,
    currentIndex,
    length,
    depth,
    spacing,
  );
  return (
    <div
      key={service.id}
      id={service.title}
      style={{
        ...styles,
        ...style,
        transition: "transform 0.3s ease, opacity 0.3s ease",
        position: "absolute",
        top: 0,
        left: 0,
        display: "flex",
        flexDirection: "column",
        cursor: "pointer",
        borderRadius: "0.75rem",
        padding: "1.5rem",
        zIndex,
      }}
      className={twMerge(
        "text-light-text flex flex-col items-start gap-2",
        service.background,
      )}
    >
      <div className="bg-dark-card dark:bg-dark-background border-dark-border rounded-full border p-2.5">
        {serviceIconsMap.get(service.icon)?.element}
      </div>
      <h3 className="pt-6 text-xl font-bold">{service.title}</h3>
      <span
        id="divider"
        className="bg-dark-border h-[0.1rem] w-full rounded-full"
      />
      <p className="text-sm text-pretty">{service.description}</p>
    </div>
  );
}
