"use client";
import React from "react";
import {
  CarouselContainer,
  CarouselControl,
  useCarousel,
} from "@web/_lib/components/ui/carousel";
import { services, type ServicesType } from "@web/_lib/models/services";
import { CarouselCards } from "./carousel-card";

export function ServiceCarousel(): React.ReactElement {
  const { onNext, onPrev, currentIndex } = useCarousel(services.length);
  return (
    <React.Fragment>
      <CarouselContainer height="25rem" className="md:hidden">
        {services.map((service: ServicesType, index: number) => {
          return (
            <CarouselCards
              key={service.id}
              service={service}
              data={{
                index,
                currentIndex,
                length: services.length,
                depth: 190,
                spacing: 50,
              }}
              style={{
                height: "25rem",
                width: "17.5rem",
              }}
            />
          );
        })}
      </CarouselContainer>
      <CarouselControl onNext={onNext} onPrev={onPrev} className="md:hidden" />
    </React.Fragment>
  );
}
