"use client";
import { Link as ScrollLink } from "react-scroll";
import { ChevronDownIcon } from "lucide-react";

// Types
type Heading = {
  id: string;
  depth: number;
  value: string;
};
export type TOCProps = {
  show: boolean;
  headings: Heading[];
};

export function TableOfContents({
  show = false,
  headings = [],
}: TOCProps): React.ReactElement | null {
  if (!show) return null;
  return (
    <details
      id="table-of-contents"
      open={true}
      className="text-light-text dark:text-dark-text"
    >
      <summary className="border-light-border dark:border-dark-border flex cursor-pointer items-center justify-between border-b px-6 py-4">
        <div>
          <h2 className="text-md font-semibold md:text-xl">
            Table of Contents
          </h2>
        </div>
        <ChevronDownIcon className="size-5" />
      </summary>
      <ul className="mx-4 list-disc space-y-2 px-6 py-4 text-sm md:text-base">
        {headings.length > 0 &&
          headings.map((heading: Heading) => (
            <li
              key={heading.id}
              className="cursor-pointer capitalize"
              style={{
                marginLeft: `${(heading.depth - 2) * 1.5}rem`,
              }}
            >
              <ScrollLink
                to={heading.id}
                spy={true}
                smooth={true}
                offset={-130}
                duration={500}
                className="no-underline hover:underline"
              >
                {heading.value}
              </ScrollLink>
            </li>
          ))}
      </ul>
    </details>
  );
}
