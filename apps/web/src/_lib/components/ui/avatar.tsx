"use client";
import { useState } from "react";
import Image from "next/image";
import { cn } from "@web/_lib/utils/cn";

// Types
type AvatarProps = {
  src?: string; // image url
  alt?: string; // image alt text
  fallback?: string; // text fallback (e.g. initials)
  size?: "sm" | "md" | "lg" | "xl" | "2xl" | "3xl";
  className?: string;
};

// Size Map
const sizeMap = {
  sm: "w-8 h-8 text-sm",
  md: "w-12 h-12 text-base",
  lg: "w-16 h-16 text-lg",
  xl: "w-20 h-20 text-xl",
  "2xl": "w-24 h-24 text-2xl",
  "3xl": "w-32 h-32 text-3xl",
};

// Component
export function Avatar({
  src,
  alt,
  fallback,
  size = "md",
  className,
}: AvatarProps): React.ReactElement {
  const [error, setError] = useState(false);

  return (
    <div
      className={cn(
        "text-light-text bg-light-gray-300 flex items-center justify-center overflow-hidden rounded-full font-medium",
        sizeMap[size],
        className,
      )}
    >
      {src !== undefined && !error ? (
        <div className="absolute inset-0">
          <Image
            src={src}
            alt={alt ?? "avatar"}
            fill
            sizes="100%"
            className="object-cover"
            onError={() => {
              setError(true);
            }}
          />
        </div>
      ) : (
        <span>{fallback}</span>
      )}
    </div>
  );
}
