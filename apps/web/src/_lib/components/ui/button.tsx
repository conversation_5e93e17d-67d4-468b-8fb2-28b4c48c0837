import Link, { type LinkProps } from "next/link";
import { tv, type VariantProps } from "tailwind-variants";
import { cn } from "@web/_lib/utils/cn";

// Main Styles
const button = tv({
  base: "inline-flex items-center justify-center rounded-md font-medium cursor-pointer transition-all duration-100 ease-in active:scale-95 disabled:opacity-50 disabled:cursor-not-allowed select-none",
  variants: {
    variant: {
      default:
        "bg-dark-card dark:bg-light-card dark:text-light-text text-dark-text ",
      destructive:
        "bg-light-peach-700 dark:bg-dark-peach-200 dark:hover:bg-dark-peach-50 text-dark-text hover:bg-light-peach-500",
      secondary:
        "bg-light-gray-700 dark:bg-dark-gray-200 hover:dark:bg-dark-gray-50 text-dark-text hover:bg-light-gray-500",
      outline:
        "border border-gray-300 text-light-text dark:text-dark-text dark:hover:text-light-text hover:bg-light-gray-100",
      link: "text-light-purple-700 dark:text-dark-purple-700 dark:hover:text-dark-purple-900 underline underline-offset-4 hover:text-light-purple-900 bg-transparent p-0",
    },
    size: {
      sm: "text-sm h-8 px-3",
      md: "text-base h-10 px-4",
      lg: "text-lg h-12 px-5",
      icon: "h-9 w-9 p-0", // perfect for icon-only button
    },
  },
  defaultVariants: {
    variant: "default",
    size: "md",
  },
});

// Types
type ButtonProps = VariantProps<typeof button> & {
  className?: string;
  children: React.ReactNode;
  href?: string;
  external?: boolean;
} & Omit<React.ButtonHTMLAttributes<HTMLButtonElement>, "href"> &
  Omit<React.AnchorHTMLAttributes<HTMLAnchorElement>, "href"> &
  Partial<LinkProps>;

// Component
export const Button = ({
  variant,
  size,
  className,
  href,
  external,
  children,
  ...props
}: ButtonProps): React.ReactElement => {
  const classes = cn(
    button({ variant, size }),
    // if not icon-only → add spacing between icon + text
    size !== "icon" && "gap-2",
    className,
  );

  if (href !== undefined) {
    if (external ?? false) {
      return (
        <a
          href={href}
          className={classes}
          target="_blank"
          rel="noopener noreferrer"
          {...props}
        >
          {children}
        </a>
      );
    }

    return (
      <Link href={href} {...props} className={classes}>
        {children}
      </Link>
    );
  }

  return (
    <button className={classes} {...props}>
      {children}
    </button>
  );
};
