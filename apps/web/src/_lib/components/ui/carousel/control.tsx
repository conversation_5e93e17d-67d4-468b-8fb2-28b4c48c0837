import { twMerge } from "tailwind-merge";
import { ChevronRightIcon, ChevronLeftIcon } from "lucide-react";
import { But<PERSON> } from "@web/_lib/components/ui";

export default function CarouselControl({
  onNext,
  onPrev,
  className,
}: {
  onNext: () => void;
  onPrev: () => void;
  className?: string;
}): React.ReactElement {
  return (
    <div id="control" className={twMerge("flex items-center gap-2", className)}>
      <Button
        onClick={onPrev}
        size="icon"
        className="bg-light-card rounded-full"
      >
        <ChevronLeftIcon className="text-light-text" />
      </Button>
      <Button
        onClick={onNext}
        size="icon"
        className="bg-light-card rounded-full"
      >
        <ChevronRightIcon className="text-light-text" />
      </Button>
    </div>
  );
}
