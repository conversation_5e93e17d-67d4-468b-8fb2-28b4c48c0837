type IconComponentProps = {
  className?: string;
  size?: number;
};
type IconLibrary = Map<string, React.ComponentType<IconComponentProps>>;
type IconProps = {
  name: string;
  size?: number;
  className?: string;
  library?: IconLibrary;
};

export function Icon({
  name,
  size = 16,
  className,
  library,
}: IconProps): React.ReactElement | undefined {
  const IconComponent = library?.get(name);
  if (IconComponent === undefined) return undefined;
  return <IconComponent className={className} size={size} />;
}
