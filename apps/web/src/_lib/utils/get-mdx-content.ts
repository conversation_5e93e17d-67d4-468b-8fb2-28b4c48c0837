import fs from "fs/promises";
import path from "path";
import { MDXRemote } from "next-mdx-remote/rsc";
import matter from "gray-matter";
import rehypeSlug from "rehype-slug";

// types
export type Heading = {
  id: string;
  depth: number;
  value: string;
};

export type MDXResult = {
  content: React.ReactElement;
  frontmatter: Record<string, string>;
  toc: Heading[];
};

// main function
export async function getMDXContent(source: string): Promise<MDXResult> {
  // read local .mdx file
  const filePath = path.join(process.cwd(), "src/_lib/markdown", source);
  const rawMDX = await fs.readFile(filePath, "utf-8");

  // extract frontmatter
  const { content: mdxContent, data: frontmatter } = matter(rawMDX);

  // extract heading for TOC
  const headings: Heading[] = [];
  const headingRegex = /^(#{1,3})\s+(.*)/gm;
  let match;
  while ((match = headingRegex.exec(mdxContent)) !== null) {
    const prefix = match[1] ?? "";
    const title = match[2] ?? "";

    if (prefix !== "" && title !== "") {
      // generate the same ID that rehype-slug would generate
      const id = title
        .toLowerCase()
        .trim()
        .replace(/[^\w\s-]/g, "")
        .replace(/[\s_-]+/g, "-")
        .replace(/^-+|-+$/g, "");

      headings.push({
        id,
        depth: prefix.length,
        value: title,
      });
    }
  }

  // compile mdx
  const content = await MDXRemote({
    source: mdxContent,
    options: {
      parseFrontmatter: false,
      mdxOptions: {
        rehypePlugins: [rehypeSlug],
      },
    },
  });

  return {
    content,
    frontmatter,
    toc: headings,
  };
}
