import fs from "fs/promises";
import path from "path";
import matter from "gray-matter";
import type { Metadata } from "next";

// Types
export type FrontmatterMetadata = {
  title?: string;
  description?: string;
  keywords?: string | string[];
  author?: string;
  image?: string;
  [key: string]: string | string[] | undefined;
};

export async function getMDXMetadata(source: string): Promise<Metadata> {
  // Read local .mdx file
  const filePath = path.join(process.cwd(), "src/_lib/markdown", source);
  const rawMDX = await fs.readFile(filePath, "utf-8");

  // Extract frontmatter
  const { data } = matter(rawMDX);
  const frontmatter = data as FrontmatterMetadata;

  // Build metadata object from frontmatter
  const metadata: Metadata = {};

  // Handle Title and Description
  if (frontmatter.title !== undefined) {
    metadata.title = frontmatter.title;
  }
  if (frontmatter.description !== undefined) {
    metadata.description = frontmatter.description;
  }

  // Handle Keywords
  if (frontmatter.keywords !== undefined) {
    metadata.keywords = Array.isArray(frontmatter.keywords)
      ? frontmatter.keywords
      : [frontmatter.keywords];
  }

  // Handle OpenGraph image
  if (frontmatter.image !== undefined) {
    metadata.openGraph = {
      images: [
        {
          url: frontmatter.image,
          alt: frontmatter.title ?? "",
        },
      ],
    };
  }

  // Handle Twitter image
  if (frontmatter.image !== undefined) {
    metadata.twitter = {
      images: [
        {
          url: frontmatter.image,
          alt: frontmatter.title ?? "",
        },
      ],
    };
  }

  // Handle Author
  if (frontmatter.author !== undefined) {
    metadata.authors = [
      {
        name: frontmatter.author,
      },
    ];
  }

  return metadata;
}
