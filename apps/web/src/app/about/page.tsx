import type { Metada<PERSON> } from "next";
import { Download } from "lucide-react";
import { <PERSON><PERSON>, Button } from "@web/_lib/components/ui";
import { getMDXMetadata, getMDXContent } from "@web/_lib/utils";
import { TableOfContents } from "@web/_lib/components/features/table-of-contents";

// Metadata
export async function generateMetadata(): Promise<Metadata> {
  const metadata = await getMDXMetadata("curriculum-vitae.mdx");
  return metadata;
}

export default async function About(): Promise<React.ReactElement> {
  const { content, toc } = await getMDXContent("curriculum-vitae.mdx");
  return (
    <main className="m-auto flex h-full w-full max-w-7xl flex-col gap-4 pb-20 md:flex-row md:items-start">
      <nav className="dark:bg-dark-card bg-light-card sticky top-20 rounded-sm opacity-95 shadow-2xs backdrop-blur-sm md:top-24 md:w-[25%]">
        <TableOfContents show={true} headings={toc} />
      </nav>
      <aside className="flex-1">
        <section className="text-light-text dark:text-dark-text flex items-start justify-between gap-4 px-6 py-4">
          <div id="profile-info" className="flex items-center gap-4">
            <Avatar fallback="A" className="size-14 md:size-20" />
            <div className="flex flex-col gap-1">
              <h3 className="text-xl font-bold md:text-2xl">Ali Muksin</h3>
              <p className="text-base italic md:text-lg">Software Engineer</p>
            </div>
          </div>
          <div
            id="link-list"
            className="flex list-none flex-col items-end gap-2 text-sm"
          >
            <p><EMAIL></p>
            <p>+6285946646906</p>
            <Button size="sm" className="no-underline">
              <Download className="size-4" />
              Download CV
            </Button>
          </div>
        </section>
        <p className="my-1 px-6 text-justify text-sm md:text-base">
          Creative and passionate Software Engineer with experience in diverse
          tech stacks and programming languages. Known for a solid foundation in
          software architecture and problem-solving, with a commitment to
          building efficient, scalable, and user-focused applications.
        </p>
        <div className="border-b-dark-gray-100 mx-6 my-4 border-b-[0.05px]" />
        <section className="prose dark:prose-invert max-w-none px-6">
          {content}
        </section>
      </aside>
    </main>
  );
}
